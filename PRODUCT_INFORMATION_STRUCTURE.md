# 📋 Standardized Product Information Structure

All product pages now follow the **exact same information structure** with **different content** for each product.

## 🎯 **Information Categories (Same for All Products)**

### 1. **Basic Product Information**
- **Product Name** (English)
- **Turkish Name** (Türkçe)
- **Tagline** (Descriptive phrase)
- **Scientific Name** (Latin name)

### 2. **Visual Assets (4 Required Images)**
- **Hero Image** - Full-screen background
- **Packaging Image** - Product packaging/presentation
- **Lifestyle Image** - Product in use/cooking context
- **Raw Ingredient Image** - Whole plant/ingredient

### 3. **Key Product Specs (Technical Table)**
- **Optimal Harvest** - "X days (Y-Z cm)"
- **Shelf Life** - "X days (Y-Z°C)"
- **Calories** - "X kcal/100g"
- **Nutritional Value** - Key vitamins/minerals
- **Water Consumption** - "X.XL / 100g production"

### 4. **Chef's Golden Rules (Exactly 5 Rules)**
- Practical cooking instructions
- Storage and handling tips
- Temperature and timing guidance
- Flavor pairing advice
- Presentation and service tips

### 5. **Critical Chef's Note**
- Important warning or critical information
- Temperature sensitivity
- Cooking limitations
- Storage requirements

### 6. **Flavor Pairings (3 Categories, 4 Items Each)**
- **Dairy Pairings** - Cheese, yogurt, cream products
- **Fish Pairings** - Seafood and fish varieties
- **Citrus Pairings** - Citrus fruits and acids

### 7. **Taste Profile (3-Stage Journey)**
- **Start** - Initial taste impression
- **Middle** - Mid-palate experience
- **Finish** - Aftertaste characteristics

## 🌈 **Product-Specific Content Examples**

### 🔴 **Red Radish**
- **Tagline**: "Bold & Peppery"
- **Critical Note**: Cooking warning about texture loss
- **Taste Profile**: Sharp → Intense → Clean
- **Color Theme**: Red

### 🟢 **Daikon Radish**
- **Tagline**: "Spicy & Crisp"
- **Critical Note**: Temperature sensitivity
- **Taste Profile**: Clean → Crisp → Subtle
- **Color Theme**: Green

### 🟣 **Perilla**
- **Tagline**: "Aromatic & Unique"
- **Critical Note**: Essential oil preservation
- **Taste Profile**: Aromatic → Complex → Lingering
- **Color Theme**: Purple

### 🔴 **Amaranth**
- **Tagline**: "Vibrant & Earthy"
- **Critical Note**: Color preservation
- **Taste Profile**: Mild → Earthy → Refreshing
- **Color Theme**: Red/Pink

## 📐 **Z-Pattern Layout Structure**

### **Section 1**: Hero Section
- Full-screen product image
- Product name and tagline
- Rating and harvest time
- Call-to-action button

### **Section 2**: Key Information (Left → Right)
- **Left**: Key Product Specs table
- **Right**: Packaging image

### **Section 3**: Usage & Chef's Notes (Left → Right)
- **Left**: Lifestyle image
- **Right**: Golden Rules + Critical Note

### **Section 4**: Flavor Profile (Left → Right)
- **Left**: Flavor Pairings (3 categories)
- **Right**: Taste Profile infographic

### **Section 5**: Call-to-Action
- Product-specific headline
- Contact information
- Navigation buttons

## ✅ **Benefits of Standardization**

1. **Consistent User Experience** - Same information structure across all products
2. **Easy Content Management** - Clear template for adding new products
3. **Professional Presentation** - Uniform design and information hierarchy
4. **SEO Optimization** - Structured data and consistent metadata
5. **Mobile Responsive** - Same responsive design patterns
6. **Brand Consistency** - Unified visual and content approach

## 🔄 **Adding New Products**

To add a new product, simply:
1. Follow the `StandardizedProductData` interface
2. Fill in product-specific content for each category
3. Choose appropriate color theme
4. Use the same Z-pattern layout structure
5. Ensure all 4 required images are available

This structure ensures every product page provides the same comprehensive information while maintaining unique, product-specific content and personality.
