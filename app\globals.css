@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@100;200;300;400;500;600;700;800;900&family=Orbitron:wght@400;500;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Brand Font - Montserrat Primary with Orbitron Accents */

/* Apply Montserrat to all headings for modern, clean hierarchy */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600; /* Semi-bold by default for headings */
  letter-spacing: -0.025em; /* Tight letter spacing for modern look */
}

/* Navigation links with Montserrat for clean readability */
nav a,
.nav-link,
.navigation-item {
  font-family: 'Montserrat', sans-serif;
  font-weight: 500; /* Medium weight for navigation */
  letter-spacing: 0.025em;
}

/* Primary buttons and CTAs with Montserrat */
.primary-button,
button[class*="bg-green"],
button[class*="bg-red"],
button[class*="bg-purple"],
.btn-primary,
.cta-button {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600; /* Semi-bold weight for buttons */
  letter-spacing: 0.025em;
}

/* Logo text styling - Keep Orbitron for brand identity */
.logo-text,
.brand-name {
  font-family: 'Orbitron', sans-serif;
  font-weight: 700; /* Bold for logo */
  letter-spacing: 0.05em; /* More spacing for logo impact */
}

/* Badge and tag elements */
.badge,
.tag,
.label {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  letter-spacing: 0.025em;
}

/* Ensure body text uses Montserrat for consistency */
body, p, .description, .content, .text-content {
  font-family: 'Montserrat', sans-serif;
  font-weight: 400;
  line-height: 1.6; /* Better readability for body text */
}

body {
  font-family: 'Montserrat', sans-serif;
  background-color: transparent;
}

/* Montserrat Font Weight Utilities */
.font-montserrat-thin { font-family: 'Montserrat', sans-serif; font-weight: 100; letter-spacing: 0.05em; }
.font-montserrat-extralight { font-family: 'Montserrat', sans-serif; font-weight: 200; letter-spacing: 0.05em; }
.font-montserrat-light { font-family: 'Montserrat', sans-serif; font-weight: 300; letter-spacing: 0.025em; }
.font-montserrat-regular { font-family: 'Montserrat', sans-serif; font-weight: 400; }
.font-montserrat-medium { font-family: 'Montserrat', sans-serif; font-weight: 500; }
.font-montserrat-semibold { font-family: 'Montserrat', sans-serif; font-weight: 600; }
.font-montserrat-bold { font-family: 'Montserrat', sans-serif; font-weight: 700; }
.font-montserrat-extrabold { font-family: 'Montserrat', sans-serif; font-weight: 800; }
.font-montserrat-black { font-family: 'Montserrat', sans-serif; font-weight: 900; }

/* Special Montserrat Thin 100 Style - Like in your image */
.montserrat-thin-100 {
  font-family: 'Montserrat', sans-serif;
  font-weight: 100;
  letter-spacing: 0.1em;
  font-size: clamp(1.5rem, 4vw, 3rem);
  line-height: 1.2;
}

/* Montserrat Variable Font - Your requested style */
.montserrat-variable {
  font-family: "Montserrat", sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
}

/* Responsive Montserrat Variable for different text sizes */
.montserrat-variable-hero {
  font-family: "Montserrat", sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
  font-size: clamp(2rem, 5vw, 4rem);
  line-height: 1.1;
}

.montserrat-variable-heading {
  font-family: "Montserrat", sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
  font-size: clamp(1.5rem, 3vw, 2.5rem);
  line-height: 1.2;
}

.montserrat-variable-text {
  font-family: "Montserrat", sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
  font-size: clamp(1rem, 2vw, 1.25rem);
  line-height: 1.4;
}

#tsparticles {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -2; /* Moved behind the new plexus background */
}

/* Main content container transparency for plexus background visibility */
.min-h-screen {
  background: transparent !important;
  position: relative;
  z-index: 2;
}

/* Hero section with new microgreens background */
.hero-section {
  background-image:
    linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)),
    url('/images/hero-microgreens.jpg');
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed; /* Creates subtle parallax effect */
  /* Performance optimizations */
  will-change: transform;
  transform: translateZ(0); /* Force hardware acceleration */
}

/* Remove the separate image and overlay elements for hero */
.hero-section .absolute.inset-0 {
  display: none !important;
}

/* Optimize for mobile - remove fixed attachment on small screens */
@media (max-width: 768px) {
  .hero-section {
    background-attachment: scroll;
  }
}

/* Product image placeholders and optimizations */
.product-image-placeholder {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #bae6fd 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #0369a1;
  font-weight: 600;
  text-align: center;
}

/* Smooth image loading */
.product-image {
  transition: opacity 0.3s ease-in-out;
}

.product-image[data-loaded="false"] {
  opacity: 0;
}

.product-image[data-loaded="true"] {
  opacity: 1;
}

/* Advanced animations for product pages */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Animation classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.5s ease-out;
}

/* Staggered animations */
.animate-stagger-1 {
  animation-delay: 0.1s;
}

.animate-stagger-2 {
  animation-delay: 0.2s;
}

.animate-stagger-3 {
  animation-delay: 0.3s;
}

.animate-stagger-4 {
  animation-delay: 0.4s;
}

/* Loading shimmer effect */
.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Enhanced hover effects */
.product-card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-card-hover:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.focus-ring:focus {
  outline: 2px solid #10b981;
  outline-offset: 2px;
}

/* Ensure navigation stays on top */
nav {
  position: relative;
  z-index: 50 !important;
}

/* Ensure all content sections have proper layering */
section {
  position: relative;
  z-index: 2;
}

/* Make sure text content is visible above plexus */
.relative.z-10 {
  z-index: 10 !important;
}

/* Override any white or colored backgrounds on main containers */
.bg-white,
.bg-gray-50,
.bg-gray-100 {
  background: transparent !important;
}

/* Ensure body background is transparent */
body {
  background: transparent !important;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
