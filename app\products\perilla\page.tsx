"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft, Star, Clock, CheckCircle, Sparkles, ChefHat, Utensils, Menu, X, Languages, AlertTriangle, Thermometer, Droplets } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { useState } from "react"

// Product data with enhanced structure for Z-pattern layout
const productData = {
  id: "perilla",
  name: "Perilla",
  turkishName: "Perilla",
  tagline: "Aromatic & Unique",
  scientificName: "Perilla frutescens",
  heroImage: "/images/products/perilla-hero.jpg",
  packagingImage: "/images/products/perilla-packaging.jpg",
  lifestyleImage: "/images/products/perilla-lifestyle.jpg",
  rawIngredientImage: "/images/products/whole-perilla.jpg",

  // Key Product Specs
  specs: {
    optimalHarvest: "18 days (8-12cm)",
    shelfLife: "7 days (2-4°C)",
    calories: "35 kcal/100g",
    nutritionalValue: "Omega-3, Iron, Calcium",
    waterConsumption: "0.6L / 100g production"
  },

  // Chef's Golden Rules
  chefRules: [
    "Essential for authentic Asian cuisine",
    "Perfect for wrapping grilled meats",
    "Excellent in fermentation applications",
    "Use fresh in salads and noodle dishes",
    "Store in cool, humid conditions"
  ],

  // Critical Chef's Note
  criticalNote: "Aromatic Preservation: Essential oils are volatile and sensitive to heat. Best used fresh or added at the end of cooking. Avoid prolonged exposure to direct sunlight.",

  // Flavor Pairings
  flavorPairings: {
    dairy: ["Soft Cheese", "Yogurt", "Butter", "Cream"],
    fish: ["Mackerel", "Salmon", "Tuna", "Sea Bass"],
    citrus: ["Lime", "Lemon", "Yuzu", "Sudachi"]
  },

  // Taste Profile Journey
  tasteProfile: {
    start: "Fresh mint opening",
    middle: "Complex herbal notes",
    finish: "Subtle anise finish"
  }
}

export default function PerillaPage() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [language, setLanguage] = useState('tr')

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen)
  }

  const toggleLanguage = () => {
    setLanguage(language === 'en' ? 'tr' : 'en')
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation - Matching Main Page Header */}
      <nav className="fixed top-0 w-full bg-black/90 backdrop-blur-sm z-50 border-b border-gray-800">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            {/* Left Side - Logo */}
            <div className="flex items-center gap-3">
              <div className="w-10 h-10">
                <Image
                  src="/images/pyramid-logo.svg"
                  alt="WHOLEGREEN"
                  width={40}
                  height={40}
                  className="w-full h-full"
                />
              </div>
              <Image
                src="/images/wholegreen-logo.png"
                alt="WHOLEGREEN"
                width={120}
                height={24}
                className="h-6 w-auto"
              />
            </div>

            {/* Right Side - Full Navigation Menu */}
            <div className="hidden md:flex items-center space-x-8">
              <Link href="/" className="text-white hover:text-green-400 transition-colors font-montserrat font-medium">
                ANA SAYFA
              </Link>
              <Link href="/products" className="text-green-400 hover:text-green-300 transition-colors font-montserrat font-medium">
                ÜRÜNLER
              </Link>
              <Link href="/#about" className="text-white hover:text-green-400 transition-colors font-montserrat font-medium">
                HAKKIMIZDA
              </Link>
              <Link href="/#services" className="text-white hover:text-green-400 transition-colors font-montserrat font-medium">
                HİZMETLER
              </Link>
              <Link href="/contact" className="text-white hover:text-green-400 transition-colors font-montserrat font-medium">
                İLETİŞİM
              </Link>

              {/* Language Toggle */}
              <button
                onClick={toggleLanguage}
                className="flex items-center text-white hover:text-green-400 transition-colors font-montserrat font-medium"
              >
                <Languages className="w-4 h-4 mr-1" />
                {language === 'en' ? 'TR' : 'EN'}
              </button>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <button
                onClick={toggleMobileMenu}
                className="text-white hover:text-green-400 transition-colors"
              >
                {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden bg-black/95 backdrop-blur-sm border-t border-gray-800">
            <div className="container mx-auto px-4 py-4 space-y-4">
              <Link
                href="/"
                className="block text-white hover:text-green-400 transition-colors font-montserrat font-medium py-2"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                ANA SAYFA
              </Link>
              <Link
                href="/products"
                className="block text-green-400 hover:text-green-300 transition-colors font-montserrat font-medium py-2"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                ÜRÜNLER
              </Link>
              <Link
                href="/#about"
                className="block text-white hover:text-green-400 transition-colors font-montserrat font-medium py-2"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                HAKKIMIZDA
              </Link>
              <Link
                href="/#services"
                className="block text-white hover:text-green-400 transition-colors font-montserrat font-medium py-2"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                HİZMETLER
              </Link>
              <Link
                href="/contact"
                className="block text-white hover:text-green-400 transition-colors font-montserrat font-medium py-2"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                İLETİŞİM
              </Link>

              {/* Mobile Language Toggle */}
              <button
                onClick={toggleLanguage}
                className="flex items-center text-white hover:text-green-400 transition-colors font-montserrat font-medium py-2"
              >
                <Languages className="w-4 h-4 mr-2" />
                {language === 'en' ? 'TR' : 'EN'}
              </button>
            </div>
          </div>
        )}
      </nav>

      {/* 1. Hero Section - Full Background Image */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden pt-16">
        {/* Background Image */}
        <div className="absolute inset-0">
          <Image
            src={productData.heroImage}
            alt={`${productData.name} Hero`}
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/40 to-black/60"></div>
        </div>

        {/* Content */}
        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
          <Link
            href="/products"
            className="inline-flex items-center text-purple-200 hover:text-white mb-8 transition-colors font-medium"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Ürünlere Geri Dön
          </Link>

          <div className="mb-6">
            <Badge className="mb-4 bg-purple-600 text-white border-purple-500 px-4 py-2">
              <Sparkles className="w-4 h-4 mr-2" />
              {productData.tagline}
            </Badge>
          </div>

          <h1 className="text-5xl md:text-7xl font-bold mb-4 leading-tight font-orbitron">
            {productData.name}
            <span className="block text-3xl md:text-4xl font-normal text-purple-200 mt-2 italic">
              {productData.scientificName}
            </span>
          </h1>

          <div className="flex items-center justify-center gap-6 mb-8 text-lg">
            <div className="flex items-center">
              <Star className="w-5 h-5 mr-2 fill-yellow-400 text-yellow-400" />
              <span className="font-semibold">4.9/5</span>
            </div>
            <div className="flex items-center">
              <Clock className="w-5 h-5 mr-2" />
              14-21 days
            </div>
          </div>

          <Button size="lg" className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-4 text-lg">
            <Link href="/contact" className="flex items-center">
              Daha Fazla Bilgi
              <ArrowLeft className="ml-2 h-5 w-5 rotate-180" />
            </Link>
          </Button>
        </div>
      </section>

      {/* 2. Key Information Section - First Row of Z-Pattern */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            {/* Left Side - Key Product Specs */}
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-8 font-orbitron">
                Key Product Specs
              </h2>

              <Card className="border-0 shadow-lg bg-white">
                <CardContent className="p-8">
                  <div className="space-y-6">
                    <div className="flex justify-between items-center py-3 border-b border-gray-100">
                      <span className="font-semibold text-gray-700">Optimal Harvest:</span>
                      <span className="text-purple-600 font-bold">{productData.specs.optimalHarvest}</span>
                    </div>
                    <div className="flex justify-between items-center py-3 border-b border-gray-100">
                      <span className="font-semibold text-gray-700">Shelf Life:</span>
                      <span className="text-purple-600 font-bold">{productData.specs.shelfLife}</span>
                    </div>
                    <div className="flex justify-between items-center py-3 border-b border-gray-100">
                      <span className="font-semibold text-gray-700">Calories:</span>
                      <span className="text-purple-600 font-bold">{productData.specs.calories}</span>
                    </div>
                    <div className="flex justify-between items-center py-3 border-b border-gray-100">
                      <span className="font-semibold text-gray-700">Nutritional Value:</span>
                      <span className="text-purple-600 font-bold">{productData.specs.nutritionalValue}</span>
                    </div>
                    <div className="flex justify-between items-center py-3">
                      <span className="font-semibold text-gray-700">Water Consumption:</span>
                      <span className="text-purple-600 font-bold">{productData.specs.waterConsumption}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Right Side - Packaging Image */}
            <div>
              <div className="relative aspect-[4/3] rounded-xl overflow-hidden shadow-xl">
                <Image
                  src={productData.packagingImage}
                  alt="Perilla Packaging"
                  width={600}
                  height={450}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                <div className="absolute bottom-4 left-4 text-white">
                  <p className="text-sm font-medium">Professional Packaging</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Detailed Information Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
            {/* Left Column - Gallery */}
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-8">Product Gallery</h3>
              <div className="space-y-6">
                {productData.galleryImages.map((image, index) => (
                  <div key={index} className="relative aspect-[4/3] rounded-lg overflow-hidden shadow-lg">
                    <Image
                      src={image}
                      alt={`${productData.name} ${index + 1}`}
                      fill
                      className="object-cover hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Right Column - Detailed Content */}
            <div className="space-y-12">
              {/* Tasting Profile */}
              <Card className="border-0 shadow-lg">
                <CardContent className="p-8">
                  <div className="flex items-center mb-4">
                    <ChefHat className="w-6 h-6 text-purple-600 mr-3" />
                    <h3 className="text-2xl font-bold text-gray-900">Tasting Profile</h3>
                  </div>
                  <p className="text-gray-700 leading-relaxed text-lg">
                    {productData.tastingProfile}
                  </p>
                </CardContent>
              </Card>

              {/* Appearance */}
              <Card className="border-0 shadow-lg">
                <CardContent className="p-8">
                  <div className="flex items-center mb-4">
                    <Sparkles className="w-6 h-6 text-purple-600 mr-3" />
                    <h3 className="text-2xl font-bold text-gray-900">Appearance</h3>
                  </div>
                  <p className="text-gray-700 leading-relaxed text-lg">
                    {productData.appearance}
                  </p>
                </CardContent>
              </Card>

              {/* Nutritional Highlights */}
              <Card className="border-0 shadow-lg">
                <CardContent className="p-8">
                  <div className="flex items-center mb-6">
                    <CheckCircle className="w-6 h-6 text-purple-600 mr-3" />
                    <h3 className="text-2xl font-bold text-gray-900">Nutritional Highlights</h3>
                  </div>
                  <ul className="space-y-3">
                    {productData.nutritionalHighlights.map((highlight, index) => (
                      <li key={index} className="flex items-start">
                        <CheckCircle className="w-5 h-5 text-purple-500 mr-3 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700 text-lg">{highlight}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              {/* Culinary Uses */}
              <Card className="border-0 shadow-lg">
                <CardContent className="p-8">
                  <div className="flex items-center mb-6">
                    <Utensils className="w-6 h-6 text-purple-600 mr-3" />
                    <h3 className="text-2xl font-bold text-gray-900">Culinary Uses</h3>
                  </div>
                  <ul className="space-y-3">
                    {productData.culinaryUses.map((use, index) => (
                      <li key={index} className="flex items-start">
                        <CheckCircle className="w-5 h-5 text-purple-500 mr-3 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700 text-lg">{use}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-purple-600 to-indigo-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">Ready to Experience Perilla?</h2>
          <p className="text-xl text-purple-100 mb-8 leading-relaxed">
            Contact us today to place your order or discuss custom growing requirements for your restaurant or business.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-purple-600 hover:bg-gray-100 hover:shadow-lg transition-all">
              <Link href="/contact" className="flex items-center">
                İletişime Geçin
                <ArrowLeft className="ml-2 h-4 w-4 rotate-180" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-purple-600 transition-all">
              <Link href="/products" className="flex items-center">
                View Other Products
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
