<svg width="1200" height="800" viewBox="0 0 1200 800" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="nasturtiumBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff7ed;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ffedd5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fed7aa;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="nasturtiumOrange" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ea580c;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#c2410c;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="nasturtiumYellow" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#eab308;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ca8a04;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="nasturtiumGreen" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#16a34a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#15803d;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="800" fill="url(#nasturtiumBg)"/>
  
  <!-- Subtle pattern -->
  <pattern id="nasturtiumPattern" x="0" y="0" width="80" height="80" patternUnits="userSpaceOnUse">
    <circle cx="40" cy="40" r="2" fill="#ea580c" opacity="0.08"/>
    <circle cx="20" cy="20" r="1.5" fill="#eab308" opacity="0.06"/>
  </pattern>
  <rect width="1200" height="800" fill="url(#nasturtiumPattern)"/>
  
  <!-- Main nasturtium flowers illustration -->
  <g transform="translate(600, 400)">
    <!-- Multiple nasturtium flowers with round leaves and vibrant flowers -->
    
    <!-- Flower 1 (Orange) -->
    <g transform="translate(-200, -50)">
      <!-- Stem -->
      <rect x="-2" y="0" width="4" height="80" fill="url(#nasturtiumGreen)" rx="2"/>
      <!-- Round leaf -->
      <circle cx="-25" cy="40" r="18" fill="url(#nasturtiumGreen)" opacity="0.8"/>
      <!-- Flower petals -->
      <g transform="translate(0, -20)">
        <ellipse cx="0" cy="-15" rx="12" ry="8" fill="url(#nasturtiumOrange)" transform="rotate(0)"/>
        <ellipse cx="12" cy="-8" rx="12" ry="8" fill="url(#nasturtiumOrange)" transform="rotate(72)"/>
        <ellipse cx="7" cy="12" rx="12" ry="8" fill="url(#nasturtiumOrange)" transform="rotate(144)"/>
        <ellipse cx="-7" cy="12" rx="12" ry="8" fill="url(#nasturtiumOrange)" transform="rotate(216)"/>
        <ellipse cx="-12" cy="-8" rx="12" ry="8" fill="url(#nasturtiumOrange)" transform="rotate(288)"/>
        <!-- Center -->
        <circle cx="0" cy="0" r="4" fill="#dc2626"/>
      </g>
    </g>
    
    <!-- Flower 2 (Yellow) -->
    <g transform="translate(-50, -80)">
      <!-- Stem -->
      <rect x="-2" y="0" width="4" height="100" fill="url(#nasturtiumGreen)" rx="2"/>
      <!-- Round leaf -->
      <circle cx="30" cy="60" r="20" fill="url(#nasturtiumGreen)" opacity="0.8"/>
      <!-- Flower petals -->
      <g transform="translate(0, -25)">
        <ellipse cx="0" cy="-18" rx="14" ry="10" fill="url(#nasturtiumYellow)" transform="rotate(0)"/>
        <ellipse cx="14" cy="-10" rx="14" ry="10" fill="url(#nasturtiumYellow)" transform="rotate(72)"/>
        <ellipse cx="9" cy="14" rx="14" ry="10" fill="url(#nasturtiumYellow)" transform="rotate(144)"/>
        <ellipse cx="-9" cy="14" rx="14" ry="10" fill="url(#nasturtiumYellow)" transform="rotate(216)"/>
        <ellipse cx="-14" cy="-10" rx="14" ry="10" fill="url(#nasturtiumYellow)" transform="rotate(288)"/>
        <!-- Center -->
        <circle cx="0" cy="0" r="5" fill="#dc2626"/>
      </g>
    </g>
    
    <!-- Flower 3 (Orange-Red) -->
    <g transform="translate(100, -60)">
      <!-- Stem -->
      <rect x="-2" y="0" width="4" height="90" fill="url(#nasturtiumGreen)" rx="2"/>
      <!-- Round leaf -->
      <circle cx="-35" cy="50" r="22" fill="url(#nasturtiumGreen)" opacity="0.8"/>
      <!-- Flower petals -->
      <g transform="translate(0, -22)">
        <ellipse cx="0" cy="-16" rx="13" ry="9" fill="#dc2626" transform="rotate(0)"/>
        <ellipse cx="13" cy="-9" rx="13" ry="9" fill="#dc2626" transform="rotate(72)"/>
        <ellipse cx="8" cy="13" rx="13" ry="9" fill="#dc2626" transform="rotate(144)"/>
        <ellipse cx="-8" cy="13" rx="13" ry="9" fill="#dc2626" transform="rotate(216)"/>
        <ellipse cx="-13" cy="-9" rx="13" ry="9" fill="#dc2626" transform="rotate(288)"/>
        <!-- Center -->
        <circle cx="0" cy="0" r="4" fill="#991b1b"/>
      </g>
    </g>
    
    <!-- Flower 4 (Yellow-Orange) -->
    <g transform="translate(250, -40)">
      <!-- Stem -->
      <rect x="-2" y="0" width="4" height="85" fill="url(#nasturtiumGreen)" rx="2"/>
      <!-- Round leaf -->
      <circle cx="25" cy="45" r="19" fill="url(#nasturtiumGreen)" opacity="0.8"/>
      <!-- Flower petals -->
      <g transform="translate(0, -18)">
        <ellipse cx="0" cy="-14" rx="11" ry="7" fill="#f59e0b" transform="rotate(0)"/>
        <ellipse cx="11" cy="-7" rx="11" ry="7" fill="#f59e0b" transform="rotate(72)"/>
        <ellipse cx="7" cy="11" rx="11" ry="7" fill="#f59e0b" transform="rotate(144)"/>
        <ellipse cx="-7" cy="11" rx="11" ry="7" fill="#f59e0b" transform="rotate(216)"/>
        <ellipse cx="-11" cy="-7" rx="11" ry="7" fill="#f59e0b" transform="rotate(288)"/>
        <!-- Center -->
        <circle cx="0" cy="0" r="3" fill="#dc2626"/>
      </g>
    </g>
    
    <!-- Additional round leaves -->
    <circle cx="-150" cy="20" r="16" fill="url(#nasturtiumGreen)" opacity="0.7"/>
    <circle cx="180" cy="30" r="17" fill="url(#nasturtiumGreen)" opacity="0.7"/>
    <circle cx="0" cy="60" r="15" fill="url(#nasturtiumGreen)" opacity="0.7"/>
  </g>
  
  <!-- Soil/growing medium base -->
  <rect x="0" y="650" width="1200" height="150" fill="#8b5cf6" opacity="0.1"/>
  
  <!-- Product label -->
  <rect x="50" y="50" width="300" height="120" fill="white" rx="15" opacity="0.95"/>
  <text x="200" y="85" text-anchor="middle" fill="#ea580c" font-family="Arial, sans-serif" font-size="24" font-weight="bold">
    Nasturtium
  </text>
  <text x="200" y="110" text-anchor="middle" fill="#eab308" font-family="Arial, sans-serif" font-size="16">
    Peppery &amp; Colorful
  </text>
  <text x="200" y="135" text-anchor="middle" fill="#6b7280" font-family="Arial, sans-serif" font-size="12" style="font-style: italic;">
    Tropaeolum majus
  </text>
  
  <!-- Decorative elements -->
  <circle cx="1050" cy="150" r="8" fill="#ea580c" opacity="0.3"/>
  <circle cx="1100" cy="200" r="6" fill="#eab308" opacity="0.4"/>
  <circle cx="150" cy="650" r="10" fill="#c2410c" opacity="0.3"/>
  <circle cx="100" cy="700" r="7" fill="#ca8a04" opacity="0.4"/>
</svg>
