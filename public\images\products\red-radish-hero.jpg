<svg width="1200" height="800" viewBox="0 0 1200 800" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="redRadishBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fef2f2;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#fee2e2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fecaca;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="redRadishRed" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#991b1b;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="redRadishPurple" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5b21b6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="redRadishGreen" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#16a34a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#15803d;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="800" fill="url(#redRadishBg)"/>
  
  <!-- Subtle pattern -->
  <pattern id="redRadishPattern" x="0" y="0" width="45" height="45" patternUnits="userSpaceOnUse">
    <circle cx="22.5" cy="22.5" r="1.5" fill="#dc2626" opacity="0.1"/>
  </pattern>
  <rect width="1200" height="800" fill="url(#redRadishPattern)"/>
  
  <!-- Main red radish microgreens illustration -->
  <g transform="translate(600, 400)">
    <!-- Multiple red radish stems with reddish-purple stems and green leaves -->
    <g transform="translate(-240, 0)">
      <!-- Stem 1 -->
      <rect x="-2" y="-85" width="4" height="170" fill="url(#redRadishPurple)" rx="2"/>
      <!-- Leaves -->
      <ellipse cx="-16" cy="-75" rx="14" ry="9" fill="url(#redRadishGreen)" transform="rotate(-22)"/>
      <ellipse cx="16" cy="-70" rx="12" ry="7" fill="url(#redRadishGreen)" transform="rotate(28)"/>
      <ellipse cx="-10" cy="-50" rx="10" ry="6" fill="url(#redRadishGreen)" transform="rotate(-15)"/>
    </g>
    
    <g transform="translate(-120, 0)">
      <!-- Stem 2 -->
      <rect x="-3" y="-95" width="6" height="190" fill="url(#redRadishRed)" rx="3"/>
      <!-- Leaves -->
      <ellipse cx="-18" cy="-85" rx="15" ry="10" fill="url(#redRadishGreen)" transform="rotate(-25)"/>
      <ellipse cx="18" cy="-80" rx="13" ry="8" fill="url(#redRadishGreen)" transform="rotate(30)"/>
      <ellipse cx="-12" cy="-60" rx="11" ry="7" fill="url(#redRadishGreen)" transform="rotate(-18)"/>
      <ellipse cx="8" cy="-45" rx="9" ry="5" fill="url(#redRadishGreen)" transform="rotate(22)"/>
    </g>
    
    <g transform="translate(0, 0)">
      <!-- Stem 3 (center, tallest) -->
      <rect x="-3" y="-105" width="6" height="210" fill="url(#redRadishPurple)" rx="3"/>
      <!-- Leaves -->
      <ellipse cx="-20" cy="-95" rx="16" ry="11" fill="url(#redRadishGreen)" transform="rotate(-30)"/>
      <ellipse cx="20" cy="-90" rx="14" ry="9" fill="url(#redRadishGreen)" transform="rotate(35)"/>
      <ellipse cx="-15" cy="-70" rx="12" ry="8" fill="url(#redRadishGreen)" transform="rotate(-20)"/>
      <ellipse cx="12" cy="-60" rx="10" ry="6" fill="url(#redRadishGreen)" transform="rotate(25)"/>
      <ellipse cx="-8" cy="-40" rx="8" ry="5" fill="url(#redRadishGreen)" transform="rotate(-12)"/>
    </g>
    
    <g transform="translate(120, 0)">
      <!-- Stem 4 -->
      <rect x="-3" y="-90" width="6" height="180" fill="url(#redRadishRed)" rx="3"/>
      <!-- Leaves -->
      <ellipse cx="-17" cy="-80" rx="14" ry="9" fill="url(#redRadishGreen)" transform="rotate(-24)"/>
      <ellipse cx="17" cy="-75" rx="12" ry="7" fill="url(#redRadishGreen)" transform="rotate(28)"/>
      <ellipse cx="-11" cy="-55" rx="10" ry="6" fill="url(#redRadishGreen)" transform="rotate(-16)"/>
    </g>
    
    <g transform="translate(240, 0)">
      <!-- Stem 5 -->
      <rect x="-2" y="-80" width="4" height="160" fill="url(#redRadishPurple)" rx="2"/>
      <!-- Leaves -->
      <ellipse cx="-15" cy="-70" rx="13" ry="8" fill="url(#redRadishGreen)" transform="rotate(-20)"/>
      <ellipse cx="15" cy="-65" rx="11" ry="6" fill="url(#redRadishGreen)" transform="rotate(25)"/>
    </g>
  </g>
  
  <!-- Soil/growing medium base -->
  <rect x="0" y="650" width="1200" height="150" fill="#8b5cf6" opacity="0.1"/>
  
  <!-- Product label -->
  <rect x="50" y="50" width="280" height="120" fill="white" rx="15" opacity="0.95"/>
  <text x="190" y="85" text-anchor="middle" fill="#dc2626" font-family="Arial, sans-serif" font-size="24" font-weight="bold">
    Red Radish
  </text>
  <text x="190" y="110" text-anchor="middle" fill="#7c3aed" font-family="Arial, sans-serif" font-size="16">
    Bold &amp; Peppery
  </text>
  <text x="190" y="135" text-anchor="middle" fill="#6b7280" font-family="Arial, sans-serif" font-size="12" style="font-style: italic;">
    Raphanus sativus
  </text>
  
  <!-- Decorative elements -->
  <circle cx="1050" cy="150" r="8" fill="#dc2626" opacity="0.3"/>
  <circle cx="1100" cy="200" r="6" fill="#7c3aed" opacity="0.4"/>
  <circle cx="150" cy="650" r="10" fill="#991b1b" opacity="0.3"/>
  <circle cx="100" cy="700" r="7" fill="#5b21b6" opacity="0.4"/>
</svg>
